# IPCrawler HTB/CTF Scanning Guide

## 🎯 Quick Start for HTB Machines

### Recommended Command for HTB:
```bash
# For comprehensive HTB scanning (run as root for UDP)
sudo ipcrawler --tags 'default,unsafe,ctf' --exclude-tags 'slow' <target_ip>

# For fast reconnaissance
ipcrawler --tags 'default,quick' <target_ip>

# For thorough penetration testing
sudo ipcrawler --tags 'default,unsafe,bruteforce' <target_ip>
```

## 🔧 Configuration Issues Fixed

### Issue 1: UDP Scanning Disabled
**Problem:** UDP scans were disabled by default, missing important services like DNS, SNMP
**Fix:** Set `enable-udp-scan = true` in config.toml
**Impact:** Now discovers UDP services that could be attack vectors

### Issue 2: Vulnerability Scans Excluded  
**Problem:** SMB vulnerability scans (MS17-010, etc.) were excluded due to 'unsafe' tag
**Fix:** Use `--tags 'default,unsafe'` or modify config.toml
**Impact:** Now detects critical vulnerabilities like EternalBlue

### Issue 3: Limited Plugin Coverage
**Problem:** Default tags only included 'safe' plugins
**Fix:** Expanded tags to include 'unsafe', 'ctf', 'bruteforce' as needed
**Impact:** More comprehensive attack surface discovery

## 📊 What Actually Ran vs What Was Missing

### ✅ Successfully Executed (153 commands):
- TCP port scanning (top ports + full range)
- HTTP/HTTPS enumeration on all 13 discovered ports
- Directory busting with multiple wordlists
- SSL/TLS certificate analysis
- Virtual host enumeration
- Spring Boot Actuator detection
- Technology fingerprinting

### ❌ Missing Components (Now Fixed):
- UDP port scanning (DNS, SNMP, DHCP, etc.)
- SMB vulnerability testing (MS17-010, MS08-067)
- Active vulnerability probing
- Bruteforce attacks (when appropriate)

## 🚀 Optimized HTB Configurations

### For Space-Constrained HTB Machines:
```toml
# config.toml optimizations
htb-mode = true
wordlist-size = "fast"
enable-udp-scan = true
tags = 'default,unsafe,ctf'
exclude-tags = 'slow'
timeout = 60                    # Shorter timeout for HTB
max-scans = 30                  # Reduce concurrent scans
```

### For Comprehensive Assessment:
```bash
# Full coverage scan
sudo ipcrawler --tags 'default,unsafe,ctf,bruteforce' \
               --timeout 120 \
               --target-timeout 60 \
               <target_ip>
```

## 🔍 HTB-Specific Plugin Tags

### Available Tag Categories:
- `default`: Standard enumeration plugins
- `unsafe`: Vulnerability scanners that might crash services
- `ctf`: CTF/HTB specific detection patterns
- `bruteforce`: Credential attacks
- `slow`: Long-running scans
- `quick`: Fast reconnaissance
- `safe`: Non-intrusive scans only

### Recommended Tag Combinations:
```bash
# Initial reconnaissance
--tags 'default,quick'

# Vulnerability assessment  
--tags 'default,unsafe'

# CTF/HTB comprehensive
--tags 'default,unsafe,ctf'

# Full penetration test
--tags 'default,unsafe,ctf,bruteforce'
```

## 🛠️ Troubleshooting HTB Scans

### Common Issues:

1. **"UDP scan requires root privileges"**
   - Solution: Run with `sudo ipcrawler`
   - Alternative: Add `--disable-sanity-checks` (not recommended)

2. **"No services were defined"**
   - Check if ports were discovered in port scan phase
   - Verify target is reachable: `ping <target>`

3. **Scans timing out**
   - Reduce timeout: `--target-timeout 30`
   - Exclude slow plugins: `--exclude-tags 'slow'`

4. **Missing vulnerability scans**
   - Include unsafe plugins: `--tags 'default,unsafe'`
   - Check if target has relevant services (SMB, etc.)

### Performance Optimization:
```bash
# For faster scans on HTB
ipcrawler --max-scans 20 \
          --exclude-tags 'slow' \
          --target-timeout 30 \
          <target_ip>
```

## 📈 Scan Completeness Verification

### Check if scan completed properly:
```bash
# Verify all phases ran
ls results/<target>/scans/

# Check for errors
cat results/<target>/scans/_errors.log

# Count executed commands
wc -l results/<target>/scans/_commands.log
```

### Expected Output Structure:
```
results/<target>/
├── scans/
│   ├── _commands.log      # All executed commands
│   ├── _errors.log        # Any errors encountered
│   ├── _patterns.log      # Detected patterns/findings
│   ├── _*_nmap.txt       # Port scan results
│   ├── tcp<port>/        # Per-service results
│   └── xml/              # XML output files
├── exploit/              # Exploitation notes
├── loot/                 # Extracted data
└── report/               # Generated reports
```

## 🎯 HTB-Specific Recommendations

1. **Always run as root** for complete UDP coverage
2. **Use comprehensive tags** for CTF environments
3. **Monitor space usage** on constrained HTB machines
4. **Check error logs** if scans seem incomplete
5. **Verify service discovery** before assuming scan failed
6. **Use scenario presets**: `--ctf`, `--pentest`, `--recon`

## 🔧 Quick Fixes Applied

The following changes have been made to improve HTB scanning:

1. **Enabled UDP scanning** in config.toml
2. **Added 'unsafe' tag** to include vulnerability scans
3. **Added HTB-specific documentation** and examples
4. **Optimized timeouts** for HTB environments
5. **Added scenario presets** for different scan types

Your scans should now be much more comprehensive and suitable for HTB machines!
